import { NextRequest, NextResponse } from "next/server"
import { cookies } from "next/headers"
import { getCertificatesWithFilters, createCertificate } from "@/lib/db"
import { nanoid } from "nanoid"

// Helper function to get user from session cookie
async function getUserFromSession() {
  try {
    const cookieStore = await cookies()
    const sessionCookie = cookieStore.get("session")
    if (!sessionCookie) {
      return null
    }

    const session = JSON.parse(sessionCookie.value)
    return session.user
  } catch (error) {
    console.error("Error parsing session:", error)
    return null
  }
}

// GET /api/certificates - Fetch certificates with filters
export async function GET(request: NextRequest) {
  try {
    const user = await getUserFromSession()
    if (!user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const search = searchParams.get("search") || ""
    const filter = searchParams.get("filter") as "all" | "favorites" | "expiring-soon" | "expired" || "all"
    const sortBy = searchParams.get("sortBy") as "name" | "dateIssued" | "expiryDate" || "expiryDate"
    const sortOrder = searchParams.get("sortOrder") as "asc" | "desc" || "asc"
    const limit = searchParams.get("limit") ? parseInt(searchParams.get("limit")!) : undefined
    const offset = searchParams.get("offset") ? parseInt(searchParams.get("offset")!) : 0

    const certificates = await getCertificatesWithFilters(user.id, {
      search,
      filter,
      sortBy,
      sortOrder,
      limit,
      offset,
    })

    // Transform dates to ISO strings for JSON serialization
    const serializedCertificates = certificates.map(cert => ({
      ...cert,
      dateIssued: cert.dateIssued.toISOString(),
      expiryDate: cert.expiryDate?.toISOString() || null,
      createdAt: cert.createdAt.toISOString(),
      updatedAt: cert.updatedAt.toISOString(),
    }))

    return NextResponse.json(serializedCertificates)
  } catch (error) {
    console.error("Error fetching certificates:", error)
    return NextResponse.json(
      { error: "Failed to fetch certificates" },
      { status: 500 }
    )
  }
}

// POST /api/certificates - Create a new certificate
export async function POST(request: NextRequest) {
  try {
    const user = await getUserFromSession()
    if (!user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const {
      name,
      issuingAuthority,
      certificateNumber,
      dateIssued,
      expiryDate,
      documentUrl,
      notes,
      isFavorite = false,
    } = body

    // Validate required fields
    if (!name || !issuingAuthority || !certificateNumber || !dateIssued) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      )
    }

    const certificateData = {
      id: nanoid(),
      name,
      issuingAuthority,
      certificateNumber,
      dateIssued: new Date(dateIssued),
      expiryDate: expiryDate ? new Date(expiryDate) : null,
      documentUrl: documentUrl || null,
      notes: notes || null,
      isFavorite,
      userId: user.id,
    }

    await createCertificate(certificateData)

    return NextResponse.json(
      { message: "Certificate created successfully", id: certificateData.id },
      { status: 201 }
    )
  } catch (error) {
    console.error("Error creating certificate:", error)
    return NextResponse.json(
      { error: "Failed to create certificate" },
      { status: 500 }
    )
  }
}
