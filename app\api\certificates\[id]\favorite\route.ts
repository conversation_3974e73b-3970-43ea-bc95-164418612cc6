import { NextRequest, NextResponse } from "next/server"
import { cookies } from "next/headers"
import { toggleCertificateFavorite, getCertificateById } from "@/lib/db"

// Helper function to get user from session cookie
async function getUserFromSession() {
  try {
    const cookieStore = await cookies()
    const sessionCookie = cookieStore.get("session")
    if (!sessionCookie) {
      return null
    }

    const session = JSON.parse(sessionCookie.value)
    return session.user
  } catch (error) {
    console.error("Error parsing session:", error)
    return null
  }
}

// POST /api/certificates/[id]/favorite - Toggle favorite status
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getUserFromSession()
    if (!user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Check if certificate exists and belongs to user
    const existingCertificate = await getCertificateById(params.id, user.id)
    if (!existingCertificate) {
      return NextResponse.json({ error: "Certificate not found" }, { status: 404 })
    }

    const result = await toggleCertificateFavorite(params.id, user.id)

    return NextResponse.json({
      message: "Favorite status updated successfully",
      isFavorite: result.isFavorite,
    })
  } catch (error) {
    console.error("Error toggling certificate favorite:", error)
    return NextResponse.json(
      { error: "Failed to update favorite status" },
      { status: 500 }
    )
  }
}
