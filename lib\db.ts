import { neon } from "@neondatabase/serverless"
import { drizzle } from "drizzle-orm/neon-http"
import { eq, and, gte, lte, sql as drizzleSql, desc, asc, ilike, or, count } from "drizzle-orm"
import { pgTable, text, timestamp, pgSchema, boolean } from "drizzle-orm/pg-core"

// Define the schema
export const users = pgTable("User", {
  id: text("id").primaryKey(),
  name: text("name").notNull(),
  email: text("email").notNull().unique(),
  password: text("password").notNull(),
  createdAt: timestamp("createdAt").defaultNow().notNull(),
  updatedAt: timestamp("updatedAt").notNull(),
})

export const certificates = pgTable("Certificate", {
  id: text("id").primaryKey(),
  name: text("name").notNull(),
  issuingAuthority: text("issuingAuthority").notNull(),
  certificateNumber: text("certificateNumber").notNull(),
  dateIssued: timestamp("dateIssued").notNull(),
  expiryDate: timestamp("expiryDate"),
  documentUrl: text("documentUrl"),
  notes: text("notes"),
  isFavorite: boolean("isFavorite").default(false).notNull(),
  createdAt: timestamp("createdAt").defaultNow().notNull(),
  updatedAt: timestamp("updatedAt").notNull(),
  userId: text("userId")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
})

// Define the neon_auth schema
export const neonAuthSchema = pgSchema("neon_auth")

export const usersSync = neonAuthSchema.table("users_sync", {
  id: text("id").primaryKey(),
  name: text("name"),
  email: text("email"),
  createdAt: timestamp("created_at"),
  updatedAt: timestamp("updated_at"),
  deletedAt: timestamp("deleted_at"),
  rawJson: text("raw_json"),
})

// Initialize the SQL client
const sql = neon(process.env.DATABASE_URL!)

// Initialize the Drizzle ORM
export const db = drizzle(sql)

// User operations
export async function getUserByEmail(email: string) {
  try {
    const result = await db.select().from(users).where(eq(users.email, email))
    return result[0] || null
  } catch (error) {
    console.error("Error getting user by email:", error)
    return null
  }
}

export async function getUserById(id: string) {
  try {
    const result = await db.select().from(users).where(eq(users.id, id))
    return result[0] || null
  } catch (error) {
    console.error("Error getting user by ID:", error)
    return null
  }
}

export async function createUser(userData: {
  id: string
  name: string
  email: string
  password: string
}) {
  try {
    const now = new Date()
    await db.insert(users).values({
      ...userData,
      createdAt: now,
      updatedAt: now,
    })
    return { success: true }
  } catch (error) {
    console.error("Error creating user:", error)
    throw error
  }
}

// Certificate operations
export async function getCertificatesByUserId(userId: string) {
  try {
    return await db.select().from(certificates).where(eq(certificates.userId, userId))
  } catch (error) {
    console.error("Error getting certificates by user ID:", error)
    return []
  }
}

export async function getCertificateById(id: string, userId: string) {
  try {
    const result = await db
      .select()
      .from(certificates)
      .where(and(eq(certificates.id, id), eq(certificates.userId, userId)))
    return result[0] || null
  } catch (error) {
    console.error("Error getting certificate by ID:", error)
    return null
  }
}

export async function getExpiringCertificates(userId: string, days: number) {
  try {
    const today = new Date()
    const futureDate = new Date()
    futureDate.setDate(today.getDate() + days)

    return await db
      .select()
      .from(certificates)
      .where(
        and(
          eq(certificates.userId, userId),
          gte(certificates.expiryDate!, today),
          lte(certificates.expiryDate!, futureDate),
        ),
      )
  } catch (error) {
    console.error("Error getting expiring certificates:", error)
    return []
  }
}

export async function getRecentCertificates(userId: string, days: number) {
  try {
    const today = new Date()
    const pastDate = new Date()
    pastDate.setDate(today.getDate() - days)

    return await db
      .select()
      .from(certificates)
      .where(and(eq(certificates.userId, userId), gte(certificates.createdAt, pastDate)))
  } catch (error) {
    console.error("Error getting recent certificates:", error)
    return []
  }
}

export async function createCertificate(certificateData: {
  id: string
  name: string
  issuingAuthority: string
  certificateNumber: string
  dateIssued: Date
  expiryDate?: Date | null
  documentUrl?: string | null
  notes?: string | null
  isFavorite?: boolean
  userId: string
}) {
  try {
    const now = new Date()
    await db.insert(certificates).values({
      ...certificateData,
      isFavorite: certificateData.isFavorite || false,
      createdAt: now,
      updatedAt: now,
    })
    return { success: true }
  } catch (error) {
    console.error("Error creating certificate:", error)
    throw error
  }
}

export async function updateCertificate(
  id: string,
  userId: string,
  certificateData: Partial<{
    name: string
    issuingAuthority: string
    certificateNumber: string
    dateIssued: Date
    expiryDate: Date | null
    documentUrl: string | null
    notes: string | null
    isFavorite: boolean
  }>,
) {
  try {
    await db
      .update(certificates)
      .set({
        ...certificateData,
        updatedAt: new Date(),
      })
      .where(and(eq(certificates.id, id), eq(certificates.userId, userId)))
    return { success: true }
  } catch (error) {
    console.error("Error updating certificate:", error)
    throw error
  }
}

export async function deleteCertificate(id: string, userId: string) {
  try {
    await db.delete(certificates).where(and(eq(certificates.id, id), eq(certificates.userId, userId)))
    return { success: true }
  } catch (error) {
    console.error("Error deleting certificate:", error)
    throw error
  }
}

export async function countCertificates(userId: string) {
  try {
    const today = new Date()
    const thirtyDaysFromNow = new Date()
    thirtyDaysFromNow.setDate(today.getDate() + 30)

    const sevenDaysAgo = new Date()
    sevenDaysAgo.setDate(today.getDate() - 7)

    // Get expiring certificates count
    const expiringResult = await db
      .select({ count: count() })
      .from(certificates)
      .where(
        and(
          eq(certificates.userId, userId),
          gte(certificates.expiryDate!, today),
          lte(certificates.expiryDate!, thirtyDaysFromNow),
        ),
      )

    // Get active certificates count
    const activeResult = await db
      .select({ count: count() })
      .from(certificates)
      .where(and(eq(certificates.userId, userId), gte(certificates.expiryDate!, today)))

    // Get recently added count
    const recentResult = await db
      .select({ count: count() })
      .from(certificates)
      .where(and(eq(certificates.userId, userId), gte(certificates.createdAt, sevenDaysAgo)))

    return {
      expiring: Number(expiringResult[0]?.count || 0),
      active: Number(activeResult[0]?.count || 0),
      recent: Number(recentResult[0]?.count || 0),
    }
  } catch (error) {
    console.error("Error counting certificates:", error)
    return {
      expiring: 0,
      active: 0,
      recent: 0,
    }
  }
}

// Enhanced certificate operations for the improved UI
export async function getCertificatesWithFilters(
  userId: string,
  options: {
    search?: string
    filter?: "all" | "favorites" | "expiring-soon" | "expired"
    sortBy?: "name" | "dateIssued" | "expiryDate"
    sortOrder?: "asc" | "desc"
    limit?: number
    offset?: number
  } = {}
) {
  try {
    const {
      search = "",
      filter = "all",
      sortBy = "expiryDate",
      sortOrder = "asc",
      limit,
      offset = 0,
    } = options

    // Build where conditions
    const conditions = [eq(certificates.userId, userId)]

    // Apply search filter
    if (search) {
      conditions.push(
        or(
          ilike(certificates.name, `%${search}%`),
          ilike(certificates.certificateNumber, `%${search}%`),
          ilike(certificates.issuingAuthority, `%${search}%`)
        )!
      )
    }

    // Apply status filters
    const today = new Date()
    const ninetyDaysFromNow = new Date()
    ninetyDaysFromNow.setDate(today.getDate() + 90)

    if (filter === "favorites") {
      conditions.push(eq(certificates.isFavorite, true))
    } else if (filter === "expiring-soon") {
      conditions.push(
        and(
          gte(certificates.expiryDate!, today),
          lte(certificates.expiryDate!, ninetyDaysFromNow)
        )!
      )
    } else if (filter === "expired") {
      conditions.push(lte(certificates.expiryDate!, today))
    }

    // Apply sorting
    const sortColumn =
      sortBy === "name" ? certificates.name :
        sortBy === "dateIssued" ? certificates.dateIssued :
          certificates.expiryDate

    // Build and execute query
    const baseQuery = db
      .select()
      .from(certificates)
      .where(and(...conditions))
      .orderBy(sortOrder === "asc" ? asc(sortColumn) : desc(sortColumn))

    // Apply pagination if specified
    if (limit) {
      return await baseQuery.limit(limit).offset(offset)
    }

    return await baseQuery
  } catch (error) {
    console.error("Error getting certificates with filters:", error)
    return []
  }
}

// Toggle favorite status
export async function toggleCertificateFavorite(id: string, userId: string) {
  try {
    const certificate = await getCertificateById(id, userId)
    if (!certificate) {
      throw new Error("Certificate not found")
    }

    await db
      .update(certificates)
      .set({
        isFavorite: !certificate.isFavorite,
        updatedAt: new Date(),
      })
      .where(and(eq(certificates.id, id), eq(certificates.userId, userId)))

    return { success: true, isFavorite: !certificate.isFavorite }
  } catch (error) {
    console.error("Error toggling certificate favorite:", error)
    throw error
  }
}

// Bulk operations
export async function bulkDeleteCertificates(ids: string[], userId: string) {
  try {
    for (const id of ids) {
      await db
        .delete(certificates)
        .where(and(eq(certificates.id, id), eq(certificates.userId, userId)))
    }
    return { success: true }
  } catch (error) {
    console.error("Error bulk deleting certificates:", error)
    throw error
  }
}

export async function bulkUpdateCertificateFavorites(
  ids: string[],
  userId: string,
  isFavorite: boolean
) {
  try {
    for (const id of ids) {
      await db
        .update(certificates)
        .set({
          isFavorite,
          updatedAt: new Date(),
        })
        .where(and(eq(certificates.id, id), eq(certificates.userId, userId)))
    }
    return { success: true }
  } catch (error) {
    console.error("Error bulk updating certificate favorites:", error)
    throw error
  }
}
