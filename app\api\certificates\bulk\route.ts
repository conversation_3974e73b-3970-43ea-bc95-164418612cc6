import { NextRequest, NextResponse } from "next/server"
import { cookies } from "next/headers"
import { bulkDeleteCertificates, bulkUpdateCertificateFavorites } from "@/lib/db"

// Helper function to get user from session cookie
async function getUserFromSession() {
  try {
    const sessionCookie = cookies().get("session")
    if (!sessionCookie) {
      return null
    }

    const session = JSON.parse(sessionCookie.value)
    return session.user
  } catch (error) {
    console.error("Error parsing session:", error)
    return null
  }
}

// POST /api/certificates/bulk - Perform bulk operations
export async function POST(request: NextRequest) {
  try {
    const user = await getUserFromSession()
    if (!user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const { action, ids } = body

    if (!action || !ids || !Array.isArray(ids) || ids.length === 0) {
      return NextResponse.json(
        { error: "Invalid request. Action and ids array are required." },
        { status: 400 }
      )
    }

    switch (action) {
      case "delete":
        await bulkDeleteCertificates(ids, user.id)
        return NextResponse.json({
          message: `Successfully deleted ${ids.length} certificate(s)`,
        })

      case "favorite":
        const { isFavorite } = body
        if (typeof isFavorite !== "boolean") {
          return NextResponse.json(
            { error: "isFavorite must be a boolean value" },
            { status: 400 }
          )
        }
        await bulkUpdateCertificateFavorites(ids, user.id, isFavorite)
        return NextResponse.json({
          message: `Successfully updated favorite status for ${ids.length} certificate(s)`,
        })

      default:
        return NextResponse.json(
          { error: "Invalid action. Supported actions: delete, favorite" },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error("Error performing bulk operation:", error)
    return NextResponse.json(
      { error: "Failed to perform bulk operation" },
      { status: 500 }
    )
  }
}
