import { NextRequest, NextResponse } from "next/server"
import { cookies } from "next/headers"
import { getCertificateById, updateCertificate, deleteCertificate } from "@/lib/db"

// Helper function to get user from session cookie
async function getUserFromSession() {
  try {
    const cookieStore = await cookies()
    const sessionCookie = cookieStore.get("session")
    if (!sessionCookie) {
      return null
    }

    const session = JSON.parse(sessionCookie.value)
    return session.user
  } catch (error) {
    console.error("Error parsing session:", error)
    return null
  }
}

// GET /api/certificates/[id] - Get a specific certificate
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getUserFromSession()
    if (!user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const certificate = await getCertificateById(params.id, user.id)
    if (!certificate) {
      return NextResponse.json({ error: "Certificate not found" }, { status: 404 })
    }

    // Transform dates to ISO strings for JSON serialization
    const serializedCertificate = {
      ...certificate,
      dateIssued: certificate.dateIssued.toISOString(),
      expiryDate: certificate.expiryDate?.toISOString() || null,
      createdAt: certificate.createdAt.toISOString(),
      updatedAt: certificate.updatedAt.toISOString(),
    }

    return NextResponse.json(serializedCertificate)
  } catch (error) {
    console.error("Error fetching certificate:", error)
    return NextResponse.json(
      { error: "Failed to fetch certificate" },
      { status: 500 }
    )
  }
}

// PUT /api/certificates/[id] - Update a specific certificate
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getUserFromSession()
    if (!user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const {
      name,
      issuingAuthority,
      certificateNumber,
      dateIssued,
      expiryDate,
      documentUrl,
      notes,
      isFavorite,
    } = body

    // Check if certificate exists and belongs to user
    const existingCertificate = await getCertificateById(params.id, user.id)
    if (!existingCertificate) {
      return NextResponse.json({ error: "Certificate not found" }, { status: 404 })
    }

    const updateData: any = {}
    if (name !== undefined) updateData.name = name
    if (issuingAuthority !== undefined) updateData.issuingAuthority = issuingAuthority
    if (certificateNumber !== undefined) updateData.certificateNumber = certificateNumber
    if (dateIssued !== undefined) updateData.dateIssued = new Date(dateIssued)
    if (expiryDate !== undefined) updateData.expiryDate = expiryDate ? new Date(expiryDate) : null
    if (documentUrl !== undefined) updateData.documentUrl = documentUrl
    if (notes !== undefined) updateData.notes = notes
    if (isFavorite !== undefined) updateData.isFavorite = isFavorite

    await updateCertificate(params.id, user.id, updateData)

    return NextResponse.json({ message: "Certificate updated successfully" })
  } catch (error) {
    console.error("Error updating certificate:", error)
    return NextResponse.json(
      { error: "Failed to update certificate" },
      { status: 500 }
    )
  }
}

// DELETE /api/certificates/[id] - Delete a specific certificate
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getUserFromSession()
    if (!user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Check if certificate exists and belongs to user
    const existingCertificate = await getCertificateById(params.id, user.id)
    if (!existingCertificate) {
      return NextResponse.json({ error: "Certificate not found" }, { status: 404 })
    }

    await deleteCertificate(params.id, user.id)

    return NextResponse.json({ message: "Certificate deleted successfully" })
  } catch (error) {
    console.error("Error deleting certificate:", error)
    return NextResponse.json(
      { error: "Failed to delete certificate" },
      { status: 500 }
    )
  }
}
