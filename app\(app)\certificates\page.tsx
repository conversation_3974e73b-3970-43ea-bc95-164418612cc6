"use client";

import { useMemo, useState, useEffect, useCallback } from "react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import {
  Download,
  FileText,
  Plus,
  Search,
  SlidersHorizontal,
  Star,
  Clock,
  AlertCircle,
  CheckCircle,
} from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuItem,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { CertificateCard } from "@/components/certificate-card";
import { CertificateTable } from "@/components/certificate-table";
import { CertificateStatusBadge } from "@/components/certificate-status-badge";
import { DeleteCertificateButton } from "@/components/delete-certificate-button";
import { Alert, AlertDescription } from "@/components/ui/alert";

// Certificate type definition (matches database schema)
type Certificate = {
  id: string;
  name: string;
  issuingAuthority: string;
  certificateNumber: string;
  dateIssued: Date;
  expiryDate: Date | null;
  documentUrl?: string | null;
  notes?: string | null;
  isFavorite: boolean;
  createdAt: Date;
  updatedAt: Date;
  userId: string;
  // Computed fields
  status: "active" | "expired" | "expiring-soon";
};

export default function CertificatesPage() {
  const router = useRouter();
  const searchParams = useSearchParams();

  // URL-based state management
  const query = searchParams?.get("query") || "";
  const urlView = searchParams?.get("view") || "grid";
  const urlFilter = searchParams?.get("filter") || "all";
  const urlSort = searchParams?.get("sort") || "expiryDate";
  const urlOrder = searchParams?.get("order") || "asc";

  // Component state
  const [view, setView] = useState<"grid" | "table">(
    urlView as "grid" | "table"
  );
  const [activeFilter, setActiveFilter] = useState<string>(urlFilter);
  const [searchQuery, setSearchQuery] = useState(query);
  const [sortBy, setSortBy] = useState<"name" | "dateIssued" | "expiryDate">(
    urlSort as any
  );
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">(
    urlOrder as "asc" | "desc"
  );
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [certificates, setCertificates] = useState<Certificate[]>([]);

  // URL state synchronization
  const updateURL = useCallback(
    (params: Record<string, string>) => {
      const newSearchParams = new URLSearchParams(searchParams?.toString());
      Object.entries(params).forEach(([key, value]) => {
        if (value) {
          newSearchParams.set(key, value);
        } else {
          newSearchParams.delete(key);
        }
      });
      router.push(`/certificates?${newSearchParams.toString()}`, {
        scroll: false,
      });
    },
    [router, searchParams]
  );

  // Debounced search
  const debouncedSearch = useCallback(
    debounce((query: string) => {
      updateURL({ query: query || "" });
    }, 300),
    [updateURL]
  );

  const handleSearchChange = (value: string) => {
    setSearchQuery(value);
    debouncedSearch(value);
  };

  // Debounce utility function
  function debounce<T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): (...args: Parameters<T>) => void {
    let timeout: NodeJS.Timeout;
    return (...args: Parameters<T>) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func(...args), wait);
    };
  }

  const handleViewCertificate = (certificateId: string) => {
    router.push(`/certificates/${certificateId}`);
  };

  // Helper function to compute certificate status
  const computeCertificateStatus = (
    expiryDate: Date | null
  ): "active" | "expired" | "expiring-soon" => {
    if (!expiryDate) return "active";

    const today = new Date();
    const diffTime = expiryDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays < 0) return "expired";
    if (diffDays <= 90) return "expiring-soon";
    return "active";
  };

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl/Cmd + K to focus search
      if ((event.ctrlKey || event.metaKey) && event.key === "k") {
        event.preventDefault();
        const searchInput = document.querySelector(
          'input[type="search"]'
        ) as HTMLInputElement;
        searchInput?.focus();
      }

      // Ctrl/Cmd + N to add new certificate
      if ((event.ctrlKey || event.metaKey) && event.key === "n") {
        event.preventDefault();
        router.push("/certificates/new");
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [router]);

  useEffect(() => {
    const loadCertificates = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Authentication is handled by middleware, so we can proceed with API call

        // Build API URL with current filters
        const params = new URLSearchParams();
        if (searchQuery) params.set("search", searchQuery);
        if (activeFilter !== "all") params.set("filter", activeFilter);
        params.set("sortBy", sortBy);
        params.set("sortOrder", sortOrder);

        const response = await fetch(`/api/certificates?${params.toString()}`, {
          credentials: "include", // Ensure cookies are sent
        });

        if (!response.ok) {
          console.error(
            `API request failed: ${response.status} ${response.statusText}`
          );
          if (response.status === 401) {
            console.log("Unauthorized - redirecting to login");
            router.push("/login");
            return;
          }
          const errorText = await response.text();
          console.error("API error response:", errorText);
          throw new Error(`Failed to fetch certificates: ${response.status}`);
        }

        const certificatesData = await response.json();
        console.log(
          `Successfully fetched ${certificatesData.length} certificates from API`
        );

        // Transform API response to match our Certificate type
        const transformedCertificates: Certificate[] = certificatesData.map(
          (cert: any) => ({
            ...cert,
            dateIssued: new Date(cert.dateIssued),
            expiryDate: cert.expiryDate ? new Date(cert.expiryDate) : null,
            createdAt: new Date(cert.createdAt),
            updatedAt: new Date(cert.updatedAt),
            status: computeCertificateStatus(
              cert.expiryDate ? new Date(cert.expiryDate) : null
            ),
          })
        );

        setCertificates(transformedCertificates);
        console.log(
          `Transformed and set ${transformedCertificates.length} certificates in state`
        );
        setIsLoading(false);
      } catch (err) {
        console.error("Error loading certificates:", err);
        setError("Failed to load certificates. Please try again.");
        setIsLoading(false);
      }
    };

    loadCertificates();
  }, [router, searchQuery, activeFilter, sortBy, sortOrder]);

  // Since filtering and sorting is now handled by the API,
  // we just use the certificates directly from the API response
  const filteredAndSortedCertificates = certificates;

  const actionCards = [
    {
      title: "All",
      description: "View all certificates",
      icon: FileText,
      count: certificates.length,
      onClick: () => {
        setActiveFilter("all");
        updateURL({ filter: "all" });
      },
      variant: "outline" as const,
    },
    {
      title: "Favorites",
      description: "View your favorite certificates",
      icon: Star,
      count: certificates.filter((c) => c.isFavorite).length,
      onClick: () => {
        setActiveFilter("favorites");
        updateURL({ filter: "favorites" });
      },
      variant: "outline" as const,
    },
    {
      title: "Expiring Soon",
      description: "Certificates that need attention",
      icon: Clock,
      count: certificates.filter((c) => c.status === "expiring-soon").length,
      onClick: () => {
        setActiveFilter("expiring-soon");
        updateURL({ filter: "expiring-soon" });
      },
      variant: "outline" as const,
    },
    {
      title: "Expired",
      description: "View expired certificates",
      icon: AlertCircle,
      count: certificates.filter((c) => c.status === "expired").length,
      onClick: () => {
        setActiveFilter("expired");
        updateURL({ filter: "expired" });
      },
      variant: "outline" as const,
    },
  ];

  const formatDate = (date: Date | null) => {
    if (!date) return "No Expiry";
    return new Date(date).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const getDaysRemaining = (expiryDate: Date | null) => {
    if (!expiryDate) return null;
    const today = new Date();
    const diffTime = new Date(expiryDate).getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const getDaysRemainingText = (expiryDate: Date | null) => {
    if (!expiryDate) return "No Expiry";
    const days = getDaysRemaining(expiryDate);
    if (!days) return "No Expiry";
    if (days < 0) return `Expired ${Math.abs(days)} days ago`;
    if (days === 0) return "Expires today";
    return `${days} days remaining`;
  };

  const getDaysRemainingClass = (expiryDate: Date | null) => {
    if (!expiryDate) return "text-gray-500";
    const days = getDaysRemaining(expiryDate);
    if (!days) return "text-gray-500";
    if (days < 0) return "text-red-500";
    if (days <= 30) return "text-orange-500";
    if (days <= 90) return "text-yellow-500";
    return "text-green-500";
  };

  if (isLoading) {
    return (
      <div className="space-y-4 sm:space-y-6 p-4 sm:p-6 md:p-8">
        <div className="flex flex-col gap-4">
          <div>
            <div className="h-8 bg-gray-200 rounded animate-pulse mb-2"></div>
            <div className="h-4 bg-gray-200 rounded animate-pulse w-2/3"></div>
          </div>
        </div>
        <div className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
          {[...Array(4)].map((_, i) => (
            <div
              key={i}
              className="h-24 bg-gray-200 rounded animate-pulse"
            ></div>
          ))}
        </div>
        <div className="h-96 bg-gray-200 rounded animate-pulse"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-4 sm:space-y-6 p-4 sm:p-6 md:p-8">
        <div className="flex flex-col gap-4">
          <div>
            <h1 className="text-xl sm:text-2xl font-bold tracking-tight">
              Certificates
            </h1>
            <p className="text-sm sm:text-base text-muted-foreground">
              Manage your professional certificates and documents
            </p>
          </div>
        </div>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
        <div className="text-center">
          <Button onClick={() => window.location.reload()}>Try Again</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4 sm:space-y-6 p-4 sm:p-6 md:p-8">
      {/* Header Section */}
      <div className="flex flex-col gap-4">
        <div>
          <h1 className="text-xl sm:text-2xl font-bold tracking-tight">
            Certificates
          </h1>
          <p className="text-sm sm:text-base text-muted-foreground">
            Manage your professional certificates and documents
          </p>
        </div>

        {/* Mobile-first search and actions */}
        <div className="flex flex-col sm:flex-row gap-3 sm:gap-2">
          {/* Search - Full width on mobile */}
          <div className="relative flex-1 sm:max-w-sm">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search certificates... (⌘K)"
              className="w-full pl-8 pr-16 h-10"
              value={searchQuery}
              onChange={(e) => handleSearchChange(e.target.value)}
            />
            <kbd className="absolute right-2.5 top-2.5 hidden sm:inline-flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground opacity-100">
              <span className="text-xs">⌘</span>K
            </kbd>
          </div>

          {/* Action buttons */}
          <div className="flex gap-2 sm:gap-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  size="default"
                  className="h-10 px-3 flex-1 sm:flex-none"
                >
                  <SlidersHorizontal className="h-4 w-4 mr-2" />
                  <span className="hidden xs:inline">Sort</span>
                  <span className="sr-only">Sort options</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuLabel>Sort by</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuRadioGroup
                  value={sortBy}
                  onValueChange={(value) => {
                    setSortBy(value as any);
                    updateURL({ sort: value });
                  }}
                >
                  <DropdownMenuRadioItem value="name">
                    Name
                  </DropdownMenuRadioItem>
                  <DropdownMenuRadioItem value="dateIssued">
                    Date Issued
                  </DropdownMenuRadioItem>
                  <DropdownMenuRadioItem value="expiryDate">
                    Expiry Date
                  </DropdownMenuRadioItem>
                </DropdownMenuRadioGroup>
                <DropdownMenuSeparator />
                <DropdownMenuRadioGroup
                  value={sortOrder}
                  onValueChange={(value) => {
                    setSortOrder(value as any);
                    updateURL({ order: value });
                  }}
                >
                  <DropdownMenuRadioItem value="asc">
                    Ascending
                  </DropdownMenuRadioItem>
                  <DropdownMenuRadioItem value="desc">
                    Descending
                  </DropdownMenuRadioItem>
                </DropdownMenuRadioGroup>
              </DropdownMenuContent>
            </DropdownMenu>

            <Button asChild className="h-10 flex-1 sm:flex-none">
              <Link href="/certificates/new">
                <Plus className="h-4 w-4 mr-2" />
                <span className="hidden xs:inline">Add Certificate</span>
                <span className="xs:hidden">Add</span>
              </Link>
            </Button>
          </div>
        </div>
      </div>

      {/* Action Cards - Improved mobile layout */}
      <div className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
        {actionCards.map((card, index) => (
          <Card
            key={index}
            className={`cursor-pointer transition-all hover:shadow-md active:scale-95 ${
              (activeFilter === "all" && card.title === "All") ||
              (activeFilter === "favorites" && card.title === "Favorites") ||
              (activeFilter === "expiring-soon" &&
                card.title === "Expiring Soon") ||
              (activeFilter === "expired" && card.title === "Expired")
                ? "ring-2 ring-primary bg-primary/5"
                : ""
            }`}
            onClick={card.onClick}
          >
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 p-4 sm:p-6">
              <CardTitle className="text-xs sm:text-sm font-medium leading-tight">
                {card.title}
              </CardTitle>
              <card.icon className="h-4 w-4 text-muted-foreground flex-shrink-0" />
            </CardHeader>
            <CardContent className="p-4 sm:p-6 pt-0">
              <div className="text-xl sm:text-2xl font-bold">
                {card.count !== undefined ? card.count : ""}
              </div>
              <p className="text-xs text-muted-foreground leading-tight">
                {card.description}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Certificates List */}
      <Card>
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center p-4 sm:p-6 pb-0">
          <div className="flex items-center space-x-2 w-full sm:w-auto">
            <Tabs
              value={view}
              onValueChange={(v) => {
                setView(v as "grid" | "table");
                updateURL({ view: v });
              }}
              className="w-full sm:w-auto"
            >
              <TabsList className="grid w-full grid-cols-2 sm:w-auto">
                <TabsTrigger value="grid" className="text-sm">
                  Grid
                </TabsTrigger>
                <TabsTrigger value="table" className="text-sm">
                  Table
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        </div>

        <div className="p-4 sm:p-6">
          {view === "table" ? (
            <div className="overflow-x-auto">
              <CertificateTable
                certificates={filteredAndSortedCertificates}
                onView={handleViewCertificate}
              />
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredAndSortedCertificates.map((cert) => (
                <CertificateCard
                  key={cert.id}
                  cert={cert}
                  onView={handleViewCertificate}
                />
              ))}
            </div>
          )}

          {filteredAndSortedCertificates.length === 0 && (
            <div className="flex flex-col items-center justify-center py-8 sm:py-12 text-center">
              <FileText className="h-10 w-10 sm:h-12 sm:w-12 text-muted-foreground mb-4" />
              <h3 className="text-base sm:text-lg font-medium">
                No certificates found
              </h3>
              <p className="text-sm text-muted-foreground mb-4 max-w-sm">
                {activeFilter === "all"
                  ? "Get started by adding a new certificate."
                  : "No certificates match the selected filter."}
              </p>
              {(activeFilter !== "all" || searchQuery) && (
                <Button
                  variant="outline"
                  onClick={() => {
                    setActiveFilter("all");
                    setSearchQuery("");
                    updateURL({ filter: "all", query: "" });
                  }}
                  className="h-10"
                >
                  Clear all filters
                </Button>
              )}
            </div>
          )}
        </div>
      </Card>
    </div>
  );
}
