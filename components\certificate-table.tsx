import {
  Eye,
  Star,
  Download,
  Edit,
  ArrowUpDown,
  ChevronDown,
  ChevronUp,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface CertificateTableProps {
  certificates: Array<{
    id: string;
    name: string;
    issuingAuthority: string;
    certificateNumber: string;
    dateIssued: Date;
    expiryDate: Date | null;
    isFavorite?: boolean;
    status: "active" | "expired" | "expiring-soon";
  }>;
  onView: (id: string) => void;
}

export function CertificateTable({
  certificates,
  onView,
}: CertificateTableProps) {
  const formatDate = (date: Date | null) => {
    if (!date) return "No Expiry";
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    }).format(date);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return (
          <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
            Active
          </Badge>
        );
      case "expiring-soon":
        return (
          <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">
            Expiring Soon
          </Badge>
        );
      case "expired":
        return (
          <Badge className="bg-red-100 text-red-800 hover:bg-red-100">
            Expired
          </Badge>
        );
      default:
        return null;
    }
  };

  const getDaysUntilExpiry = (expiryDate: Date | null) => {
    if (!expiryDate) return null;
    const today = new Date();
    const expiry = new Date(expiryDate);
    const diffTime = expiry.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  return (
    <TooltipProvider>
      <div className="rounded-md border overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow className="bg-muted/50">
              <TableHead className="font-semibold">Certificate</TableHead>
              <TableHead className="font-semibold hidden sm:table-cell">
                Issuing Authority
              </TableHead>
              <TableHead className="font-semibold hidden md:table-cell">
                Certificate Number
              </TableHead>
              <TableHead className="font-semibold hidden lg:table-cell">
                Issued
              </TableHead>
              <TableHead className="font-semibold">Expires</TableHead>
              <TableHead className="font-semibold hidden sm:table-cell">
                Status
              </TableHead>
              <TableHead className="text-right font-semibold">
                Actions
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {certificates.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="h-24 text-center">
                  No certificates found.
                </TableCell>
              </TableRow>
            ) : (
              certificates.map((cert) => {
                const daysRemaining = getDaysUntilExpiry(cert.expiryDate);
                return (
                  <TableRow
                    key={cert.id}
                    className="hover:bg-muted/50 cursor-pointer"
                    onClick={() => onView(cert.id)}
                  >
                    <TableCell className="font-medium">
                      <div className="flex flex-col gap-1">
                        <div className="flex items-center gap-2">
                          {cert.isFavorite && (
                            <Star className="h-4 w-4 text-yellow-500 fill-yellow-500" />
                          )}
                          <span className="font-medium">{cert.name}</span>
                        </div>
                        {/* Mobile: Show additional info when columns are hidden */}
                        <div className="sm:hidden text-xs text-muted-foreground">
                          {cert.issuingAuthority}
                        </div>
                        <div className="md:hidden sm:block text-xs text-muted-foreground">
                          {cert.certificateNumber}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="hidden sm:table-cell">
                      {cert.issuingAuthority}
                    </TableCell>
                    <TableCell className="hidden md:table-cell">
                      {cert.certificateNumber}
                    </TableCell>
                    <TableCell className="hidden lg:table-cell">
                      {formatDate(cert.dateIssued)}
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col">
                        <span
                          className={
                            cert.status === "expired"
                              ? "text-red-600 font-medium"
                              : ""
                          }
                        >
                          {formatDate(cert.expiryDate)}
                        </span>
                        {cert.status !== "expired" &&
                          daysRemaining !== null && (
                            <span
                              className={`text-xs ${
                                cert.status === "expiring-soon"
                                  ? "text-yellow-700"
                                  : "text-green-700"
                              }`}
                            >
                              {daysRemaining} days left
                            </span>
                          )}
                        {/* Mobile: Show issued date when hidden */}
                        <div className="lg:hidden text-xs text-muted-foreground">
                          Issued: {formatDate(cert.dateIssued)}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="hidden sm:table-cell">
                      {getStatusBadge(cert.status)}
                    </TableCell>
                    <TableCell className="text-right">
                      <div
                        className="flex justify-end gap-1"
                        onClick={(e) => e.stopPropagation()}
                      >
                        {/* Mobile: Show only view button, hide others */}
                        <div className="sm:hidden">
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-9 w-9"
                            onClick={() => onView(cert.id)}
                            aria-label="View certificate"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                        </div>

                        {/* Desktop: Show all action buttons */}
                        <div className="hidden sm:flex gap-1">
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8"
                                onClick={() => onView(cert.id)}
                                aria-label="View certificate"
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>View details</TooltipContent>
                          </Tooltip>

                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8"
                                aria-label="Edit certificate"
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>Edit</TooltipContent>
                          </Tooltip>

                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8"
                                aria-label="Download certificate"
                              >
                                <Download className="h-4 w-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>Download</TooltipContent>
                          </Tooltip>
                        </div>
                      </div>
                    </TableCell>
                  </TableRow>
                );
              })
            )}
          </TableBody>
        </Table>
      </div>
    </TooltipProvider>
  );
}
